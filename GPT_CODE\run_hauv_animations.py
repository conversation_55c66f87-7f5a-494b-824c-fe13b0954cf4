#!/usr/bin/env python3
"""
HAUV力的视角转换动画运行脚本

这个脚本提供了三种不同的动画演示：
1. 基础2D演示 - 展示基本概念
2. 3D演示 - 完整的三维可视化
3. 交互式演示 - 多种角度组合的详细分析

使用方法：
python run_hauv_animations.py [选项]

选项：
--demo [2d|3d|interactive|all] : 选择要运行的演示类型
--quality [low|medium|high] : 设置渲染质量
--output-dir [路径] : 指定输出目录
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def run_manim_scene(script_path, scene_class, quality="medium", output_dir=None, use_gpu=False):
    """运行Manim场景"""

    # 质量设置映射
    quality_flags = {
        "low": ["-ql"],      # 低质量，快速预览
        "medium": ["-qm"],   # 中等质量
        "high": ["-qh"]      # 高质量
    }

    # 构建命令
    cmd = ["manim"]
    cmd.extend(quality_flags.get(quality, ["-qm"]))

    # GPU加速选项
    if use_gpu:
        cmd.extend(["--renderer=opengl"])
        print("🚀 启用GPU加速 (OpenGL渲染器)")

    if output_dir:
        cmd.extend(["-o", output_dir])

    cmd.extend([script_path, scene_class])
    
    print(f"运行命令: {' '.join(cmd)}")
    print(f"正在渲染 {scene_class}...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ {scene_class} 渲染完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 渲染失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ 错误: 未找到manim命令。请确保已安装manim:")
        print("pip install manim")
        return False

def main():
    parser = argparse.ArgumentParser(description="HAUV力的视角转换动画演示")
    parser.add_argument("--demo", 
                       choices=["2d", "3d", "interactive", "all"],
                       default="all",
                       help="选择要运行的演示类型")
    parser.add_argument("--quality",
                       choices=["low", "medium", "high"],
                       default="medium",
                       help="设置渲染质量")
    parser.add_argument("--output-dir",
                       help="指定输出目录")
    parser.add_argument("--gpu",
                       action="store_true",
                       help="启用GPU加速 (OpenGL渲染器)")
    
    args = parser.parse_args()
    
    # 获取脚本目录
    script_dir = Path(__file__).parent
    
    # 定义场景映射
    scenes = {
        "2d": {
            "script": script_dir / "HAUV_Force_Visualization.py",
            "class": "HAUVForceVisualization",
            "description": "基础2D力分解演示"
        },
        "3d": {
            "script": script_dir / "HAUV_3D_Force_Visualization.py", 
            "class": "HAUV3DForceVisualization",
            "description": "完整3D力转换演示"
        },
        "interactive": {
            "script": script_dir / "HAUV_Interactive_Demo.py",
            "class": "HAUVInteractiveDemo", 
            "description": "交互式多角度演示"
        }
    }
    
    print("🌊 HAUV力的视角转换动画演示 🌊")
    print("=" * 50)
    
    # 检查文件是否存在
    missing_files = []
    for scene_name, scene_info in scenes.items():
        if not scene_info["script"].exists():
            missing_files.append(scene_info["script"])
    
    if missing_files:
        print("❌ 错误: 以下文件不存在:")
        for file in missing_files:
            print(f"  - {file}")
        return 1
    
    # 确定要运行的场景
    if args.demo == "all":
        scenes_to_run = scenes.keys()
    else:
        scenes_to_run = [args.demo]
    
    # 运行场景
    success_count = 0
    total_count = len(scenes_to_run)
    
    for scene_name in scenes_to_run:
        scene_info = scenes[scene_name]
        print(f"\n📽️  {scene_info['description']}")
        print("-" * 30)
        
        success = run_manim_scene(
            str(scene_info["script"]),
            scene_info["class"],
            args.quality,
            args.output_dir,
            args.gpu
        )
        
        if success:
            success_count += 1
        
        print()
    
    # 总结
    print("=" * 50)
    print(f"渲染完成: {success_count}/{total_count} 个场景成功")
    
    if success_count == total_count:
        print("🎉 所有动画渲染成功!")
        print("\n💡 动画说明:")
        print("• 2D演示: 展示基本的力分解概念和三个典型场景")
        print("• 3D演示: 完整的三维空间力转换，包含相机旋转")
        print("• 交互式演示: 多种角度组合的详细数值分析")
        print("\n🔬 物理概念:")
        print("• 重力和浮力在地球坐标系中始终垂直")
        print("• 旋转矩阵将力从地球坐标系转换到机体坐标系")
        print("• 机体坐标系中的力分量产生恢复力矩")
        print("• 公式 g₁ = -R₁ᵀ(fB + fG) 实现了坐标系转换")
    else:
        print("⚠️  部分动画渲染失败，请检查错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
