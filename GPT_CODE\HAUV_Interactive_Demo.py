from manim import *
import numpy as np

class HAUVInteractiveDemo(Scene):
    def construct(self):
        # 物理参数
        self.m = 100  # 质量 kg
        self.k_s = 0.9  # 形状因子
        self.rho_water = 1000  # 水密度 kg/m³
        self.V = 0.08  # 体积 m³
        self.g = 9.81  # 重力加速度
        
        # 计算净浮力
        self.net_buoyancy = (self.m - self.k_s * self.rho_water * self.V) * self.g
        
        # 设置场景
        title = Text("HAUV力分解交互演示", font_size=32, color=BLUE)
        title.to_edge(UP)
        self.add(title)
        
        # 创建坐标系
        axes = Axes(
            x_range=[-4, 4, 1],
            y_range=[-4, 4, 1],
            x_length=6,
            y_length=6
        )
        axes.move_to(ORIGIN)
        self.add(axes)
        
        # 创建HAUV
        hauv = Rectangle(width=2.5, height=0.8, color=YELLOW, fill_opacity=0.7)
        hauv.move_to(ORIGIN)
        
        # 机体坐标系
        body_x = Arrow(ORIGIN, RIGHT * 1.5, color=RED, buff=0)
        body_y = Arrow(ORIGIN, UP * 1.5, color=GREEN, buff=0)
        body_axes = VGroup(body_x, body_y)
        
        hauv_group = VGroup(hauv, body_axes)
        hauv_group.angle = 0  # 初始化角度记录
        self.add(hauv_group)
        
        # 地球坐标系中的力
        gravity = Arrow(ORIGIN, DOWN * 2, color=ORANGE, buff=0, stroke_width=6)
        buoyancy = Arrow(ORIGIN, UP * 1.6, color=BLUE_C, buff=0, stroke_width=6)
        net_force = Arrow(ORIGIN, DOWN * 0.4, color=PURPLE, buff=0, stroke_width=8)
        
        self.add(gravity, buoyancy, net_force)
        
        # 标签
        gravity_label = Text("重力", font_size=16, color=ORANGE)
        gravity_label.next_to(gravity, DOWN)
        buoyancy_label = Text("浮力", font_size=16, color=BLUE_C)
        buoyancy_label.next_to(buoyancy, UP)
        
        self.add(gravity_label, buoyancy_label)
        
        # 参数显示
        param_group = VGroup()
        
        # 角度参数
        theta_text = MathTex("\\theta = 0°", font_size=20, color=WHITE)
        phi_text = MathTex("\\phi = 0°", font_size=20, color=WHITE)
        
        theta_text.to_edge(LEFT, buff=0.5).shift(UP * 2.5)
        phi_text.next_to(theta_text, DOWN)
        
        param_group.add(theta_text, phi_text)
        
        # 力分量显示
        force_title = Text("机体坐标系力分量:", font_size=18, color=YELLOW)
        force_title.to_edge(LEFT, buff=0.5).shift(UP * 1)
        
        fx_display = MathTex("g_{1x} = 0.0 N", font_size=16, color=RED)
        fy_display = MathTex("g_{1y} = 0.0 N", font_size=16, color=GREEN)
        fz_display = MathTex("g_{1z} = 196.2 N", font_size=16, color=BLUE)
        
        fx_display.next_to(force_title, DOWN, aligned_edge=LEFT)
        fy_display.next_to(fx_display, DOWN, aligned_edge=LEFT)
        fz_display.next_to(fy_display, DOWN, aligned_edge=LEFT)
        
        param_group.add(force_title, fx_display, fy_display, fz_display)
        self.add(param_group)
        
        # 机体坐标系中的力向量（初始为空）
        body_force_x = Arrow(ORIGIN, ORIGIN, color=RED, buff=0, stroke_width=4)
        body_force_y = Arrow(ORIGIN, ORIGIN, color=GREEN, buff=0, stroke_width=4)
        body_force_z = Arrow(ORIGIN, DOWN * 0.4, color=BLUE, buff=0, stroke_width=4)
        
        body_forces = VGroup(body_force_x, body_force_y, body_force_z)
        self.add(body_forces)
        
        # 演示不同角度
        angles_to_demo = [
            (0, 0, "水平状态"),
            (30, 0, "俯仰30°"),
            (0, 45, "翻滚45°"),
            (20, 30, "俯仰20°+翻滚30°"),
            (-15, -20, "俯仰-15°+翻滚-20°"),
            (45, 0, "大俯仰45°"),
            (0, 60, "大翻滚60°")
        ]
        
        for theta_deg, phi_deg, description in angles_to_demo:
            # 更新描述
            desc_text = Text(f"演示: {description}", font_size=20, color=YELLOW)
            desc_text.to_edge(RIGHT, buff=0.5).shift(UP * 2)
            
            if 'desc_display' in locals():
                self.remove(desc_display)
            desc_display = desc_text
            self.add(desc_display)
            
            # 转换为弧度
            theta_rad = theta_deg * PI / 180
            phi_rad = phi_deg * PI / 180
            
            # 计算旋转矩阵元素
            cos_theta = np.cos(theta_rad)
            sin_theta = np.sin(theta_rad)
            cos_phi = np.cos(phi_rad)
            sin_phi = np.sin(phi_rad)
            
            # 计算机体坐标系中的力分量
            g1x = -self.net_buoyancy * sin_theta
            g1y = self.net_buoyancy * cos_theta * sin_phi
            g1z = self.net_buoyancy * cos_theta * cos_phi
            
            # 更新HAUV姿态
            target_angle = theta_rad + phi_rad  # 简化的2D表示
            rotation_needed = target_angle - hauv_group.angle

            self.play(
                Rotate(hauv_group, rotation_needed, about_point=ORIGIN),
                run_time=1.5
            )
            hauv_group.angle = target_angle  # 更新角度记录
            
            # 更新参数显示
            new_theta_text = MathTex(f"\\theta = {theta_deg}°", font_size=20, color=WHITE)
            new_phi_text = MathTex(f"\\phi = {phi_deg}°", font_size=20, color=WHITE)
            
            new_theta_text.move_to(theta_text.get_center())
            new_phi_text.move_to(phi_text.get_center())
            
            # 更新力分量显示
            new_fx_display = MathTex(f"g_{{1x}} = {g1x:.1f} N", font_size=16, color=RED)
            new_fy_display = MathTex(f"g_{{1y}} = {g1y:.1f} N", font_size=16, color=GREEN)
            new_fz_display = MathTex(f"g_{{1z}} = {g1z:.1f} N", font_size=16, color=BLUE)
            
            new_fx_display.move_to(fx_display.get_center())
            new_fy_display.move_to(fy_display.get_center())
            new_fz_display.move_to(fz_display.get_center())
            
            self.play(
                Transform(theta_text, new_theta_text),
                Transform(phi_text, new_phi_text),
                Transform(fx_display, new_fx_display),
                Transform(fy_display, new_fy_display),
                Transform(fz_display, new_fz_display),
                run_time=1
            )
            
            # 更新机体坐标系中的力向量
            # 计算新的力向量（简化的2D表示）
            scale_factor = 0.002  # 缩放因子用于显示

            # 创建新的力向量
            if abs(g1x) > 10:
                fx_magnitude = g1x * scale_factor
                new_body_force_x = Arrow(ORIGIN, RIGHT * fx_magnitude, color=RED, buff=0, stroke_width=4)
            else:
                new_body_force_x = Arrow(ORIGIN, ORIGIN, color=RED, buff=0, stroke_width=1)

            if abs(g1y) > 10:
                fy_magnitude = g1y * scale_factor
                new_body_force_y = Arrow(ORIGIN, UP * fy_magnitude, color=GREEN, buff=0, stroke_width=4)
            else:
                new_body_force_y = Arrow(ORIGIN, ORIGIN, color=GREEN, buff=0, stroke_width=1)

            if abs(g1z) > 10:
                fz_magnitude = -g1z * scale_factor  # 负号因为向下为正
                new_body_force_z = Arrow(ORIGIN, UP * fz_magnitude, color=BLUE, buff=0, stroke_width=4)
            else:
                new_body_force_z = Arrow(ORIGIN, ORIGIN, color=BLUE, buff=0, stroke_width=1)
            
            self.play(
                Transform(body_force_x, new_body_force_x),
                Transform(body_force_y, new_body_force_y),
                Transform(body_force_z, new_body_force_z),
                run_time=1
            )
            
            # 添加恢复力矩说明
            if abs(g1x) > 10:
                moment_x_text = Text("产生俯仰恢复力矩", font_size=14, color=RED)
                moment_x_text.to_edge(RIGHT, buff=0.5).shift(DOWN * 0.5)
                self.add(moment_x_text)
            
            if abs(g1y) > 10:
                moment_y_text = Text("产生翻滚恢复力矩", font_size=14, color=GREEN)
                moment_y_text.to_edge(RIGHT, buff=0.5).shift(DOWN * 1)
                self.add(moment_y_text)
            
            self.wait(2.5)
            
            # 清除力矩说明
            if 'moment_x_text' in locals():
                self.remove(moment_x_text)
                del moment_x_text
            if 'moment_y_text' in locals():
                self.remove(moment_y_text)
                del moment_y_text
        
        # 最终总结
        summary = Text("旋转矩阵实现了力的坐标系转换", font_size=20, color=YELLOW)
        summary.to_edge(DOWN, buff=0.5)
        
        formula = MathTex("g_1 = -R_1^T(f_B + f_G)", font_size=24, color=WHITE)
        formula.next_to(summary, UP)
        
        self.play(Write(summary), Write(formula))
        self.wait(3)

if __name__ == "__main__":
    scene = HAUVInteractiveDemo()
    scene.render()
