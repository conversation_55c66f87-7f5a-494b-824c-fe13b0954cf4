#!/usr/bin/env python3
"""
GPU支持检测脚本
检测系统是否支持Manim的GPU加速功能
"""

import sys
import subprocess
import importlib.util

def check_package(package_name):
    """检查Python包是否已安装"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def get_gpu_info():
    """获取GPU信息"""
    gpu_info = []
    
    # 检查NVIDIA GPU
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    name, memory = line.split(', ')
                    gpu_info.append(f"NVIDIA {name.strip()} ({memory.strip()}MB)")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    # 检查AMD GPU (Windows)
    try:
        if sys.platform == "win32":
            result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                for line in lines:
                    line = line.strip()
                    if line and 'AMD' in line or 'Radeon' in line:
                        gpu_info.append(f"AMD {line}")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    return gpu_info

def check_opengl_support():
    """检查OpenGL支持"""
    try:
        import moderngl
        ctx = moderngl.create_context(standalone=True)
        info = {
            'version': ctx.info.get('GL_VERSION', 'Unknown'),
            'renderer': ctx.info.get('GL_RENDERER', 'Unknown'),
            'vendor': ctx.info.get('GL_VENDOR', 'Unknown')
        }
        ctx.release()
        return True, info
    except Exception as e:
        return False, str(e)

def main():
    print("🔍 GPU支持检测报告")
    print("=" * 50)
    
    # 1. 检查基础包
    print("\n📦 Python包检查:")
    packages = {
        'manim': 'Manim动画库',
        'moderngl': 'OpenGL支持',
        'PyOpenGL': 'OpenGL绑定',
        'numpy': 'NumPy数值计算'
    }
    
    missing_packages = []
    for package, description in packages.items():
        if check_package(package):
            print(f"  ✅ {package} - {description}")
        else:
            print(f"  ❌ {package} - {description} (未安装)")
            missing_packages.append(package)
    
    # 2. 检查GPU硬件
    print("\n🖥️  GPU硬件检查:")
    gpu_info = get_gpu_info()
    if gpu_info:
        for gpu in gpu_info:
            print(f"  ✅ {gpu}")
    else:
        print("  ⚠️  未检测到独立GPU或GPU信息获取失败")
        print("     (集成显卡也可能支持OpenGL)")
    
    # 3. 检查OpenGL支持
    print("\n🎮 OpenGL支持检查:")
    if check_package('moderngl'):
        opengl_ok, opengl_info = check_opengl_support()
        if opengl_ok:
            print("  ✅ OpenGL支持正常")
            print(f"     版本: {opengl_info['version']}")
            print(f"     渲染器: {opengl_info['renderer']}")
            print(f"     供应商: {opengl_info['vendor']}")
        else:
            print(f"  ❌ OpenGL初始化失败: {opengl_info}")
    else:
        print("  ❌ moderngl未安装，无法检测OpenGL支持")
    
    # 4. 系统信息
    print(f"\n💻 系统信息:")
    print(f"  操作系统: {sys.platform}")
    print(f"  Python版本: {sys.version.split()[0]}")
    
    # 5. 建议
    print("\n💡 建议:")
    
    if missing_packages:
        print("  📥 安装缺失的包:")
        for package in missing_packages:
            if package == 'PyOpenGL':
                print(f"     pip install {package} PyOpenGL_accelerate")
            else:
                print(f"     pip install {package}")
    
    if not gpu_info:
        print("  🔧 GPU优化建议:")
        print("     - 更新显卡驱动程序")
        print("     - 确保显卡支持OpenGL 3.3+")
        print("     - 集成显卡也可能支持GPU加速")
    
    # 6. 测试命令
    print("\n🧪 测试命令:")
    print("  # 测试GPU加速:")
    print("  python GPT_CODE/run_hauv_animations.py --demo 2d --gpu --quality low")
    print("\n  # 如果GPU加速失败，使用CPU渲染:")
    print("  python GPT_CODE/run_hauv_animations.py --demo 2d --quality low")
    
    # 7. 性能预期
    print("\n📊 性能预期:")
    if opengl_ok if 'opengl_ok' in locals() else False:
        print("  🚀 GPU加速可用:")
        print("     - 2D动画: 轻微提升")
        print("     - 3D动画: 显著提升 (2-5倍)")
        print("     - 交互式演示: 明显提升")
    else:
        print("  🐌 仅CPU渲染:")
        print("     - 所有动画都可正常运行")
        print("     - 3D动画可能较慢")
        print("     - 建议先运行2D和交互式演示")
    
    print("\n" + "=" * 50)
    print("检测完成！请根据上述建议配置GPU加速。")

if __name__ == "__main__":
    main()
