# Manim GPU加速配置指南

## 🚀 GPU加速选项

### 1. OpenGL渲染器 (推荐)

#### 基本使用
```bash
# 启用GPU加速运行动画
python GPT_CODE/run_hauv_animations.py --gpu

# 或者直接使用manim命令
manim --renderer=opengl -ql GPT_CODE/HAUV_Force_Visualization.py HAUVForceVisualization
```

#### 优势
- ✅ 实时渲染，可以交互
- ✅ 显著提升复杂3D场景的性能
- ✅ 支持实时预览和调试
- ✅ 内置于Manim Community版本

#### 适用场景
- 3D动画 (`HAUV_3D_Force_Visualization.py`)
- 复杂的交互式演示
- 开发和调试阶段

### 2. 硬件要求

#### 最低要求
- **GPU**: 支持OpenGL 3.3+的显卡
- **显存**: 至少1GB
- **驱动**: 最新的显卡驱动程序

#### 推荐配置
- **NVIDIA**: GTX 1060 / RTX 2060 或更高
- **AMD**: RX 580 / RX 6600 或更高
- **Intel**: Iris Xe 或更高
- **显存**: 4GB+ (用于复杂场景)

### 3. 环境配置

#### Windows
```bash
# 确保安装了最新的显卡驱动
# NVIDIA: 从官网下载GeForce Experience
# AMD: 从官网下载Radeon Software
# Intel: 从官网下载Intel Graphics Driver

# 安装支持OpenGL的Python包
pip install moderngl
pip install PyOpenGL PyOpenGL_accelerate
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3-opengl

# 安装显卡驱动
# NVIDIA
sudo apt install nvidia-driver-470  # 或更新版本

# AMD
sudo apt install mesa-vulkan-drivers

# 验证OpenGL支持
glxinfo | grep "OpenGL version"
```

#### macOS
```bash
# macOS内置OpenGL支持
# 确保系统版本 >= 10.14

# 安装依赖
pip install PyOpenGL PyOpenGL_accelerate
```

## 🔧 性能优化

### 1. 渲染器选择

```python
# 在Python代码中设置
from manim import *

# 方法1: 全局配置
config.renderer = "opengl"

# 方法2: 场景级配置
class MyScene(Scene):
    def __init__(self, renderer="opengl", **kwargs):
        super().__init__(renderer=renderer, **kwargs)
```

### 2. 质量vs性能平衡

```bash
# 开发阶段 - 优先速度
python GPT_CODE/run_hauv_animations.py --gpu --quality low

# 预览阶段 - 平衡质量和速度
python GPT_CODE/run_hauv_animations.py --gpu --quality medium

# 最终渲染 - 优先质量
python GPT_CODE/run_hauv_animations.py --quality high  # 不使用GPU，获得最高质量
```

### 3. 内存优化

```python
# 在场景中优化内存使用
class OptimizedScene(Scene):
    def construct(self):
        # 及时清理不需要的对象
        self.remove(*self.mobjects)
        
        # 使用较少的采样点
        config.frame_rate = 15  # 降低帧率
        
        # 分批处理复杂动画
        self.play(animation1)
        self.clear()  # 清理缓存
        self.play(animation2)
```

## 📊 性能对比

### 渲染时间对比 (估算)

| 场景类型 | CPU渲染 | GPU渲染 | 提升倍数 |
|---------|---------|---------|----------|
| 2D基础动画 | 30秒 | 25秒 | 1.2x |
| 3D复杂场景 | 5分钟 | 1分钟 | 5x |
| 交互式演示 | 2分钟 | 30秒 | 4x |

### 内存使用对比

| 渲染器 | 系统内存 | 显存使用 |
|--------|----------|----------|
| Cairo (CPU) | 高 | 无 |
| OpenGL (GPU) | 中 | 中等 |

## 🐛 故障排除

### 常见问题

#### 1. OpenGL不可用
```bash
# 错误信息: "OpenGL context could not be created"
# 解决方案:
# 1. 更新显卡驱动
# 2. 检查OpenGL版本
python -c "import moderngl; print(moderngl.create_context().info)"
```

#### 2. 显存不足
```bash
# 错误信息: "CUDA out of memory" 或类似
# 解决方案:
# 1. 降低渲染质量
python GPT_CODE/run_hauv_animations.py --gpu --quality low

# 2. 减少同时渲染的对象数量
# 3. 关闭其他GPU应用程序
```

#### 3. 性能没有提升
```bash
# 可能原因:
# 1. 场景过于简单，CPU已经足够快
# 2. GPU驱动过旧
# 3. 集成显卡性能有限

# 解决方案:
# 1. 在复杂3D场景中测试
# 2. 更新驱动程序
# 3. 使用独立显卡
```

### 调试命令

```bash
# 检查GPU信息
nvidia-smi  # NVIDIA
rocm-smi    # AMD

# 检查OpenGL支持
python -c "
import moderngl
ctx = moderngl.create_context()
print(f'OpenGL版本: {ctx.info[\"GL_VERSION\"]}')
print(f'渲染器: {ctx.info[\"GL_RENDERER\"]}')
print(f'供应商: {ctx.info[\"GL_VENDOR\"]}')
"

# 监控GPU使用率
# Windows: 任务管理器 -> 性能 -> GPU
# Linux: nvidia-smi -l 1
```

## 🎯 最佳实践

### 1. 开发流程
```bash
# 1. 开发阶段 - 快速迭代
python GPT_CODE/run_hauv_animations.py --demo 2d --gpu --quality low

# 2. 测试阶段 - 验证效果
python GPT_CODE/run_hauv_animations.py --demo 3d --gpu --quality medium

# 3. 最终渲染 - 高质量输出
python GPT_CODE/run_hauv_animations.py --quality high
```

### 2. 场景选择
- **2D动画**: CPU渲染通常已足够
- **3D动画**: 强烈推荐GPU加速
- **交互式演示**: GPU加速显著提升体验
- **最终发布**: 使用CPU渲染获得最高质量

### 3. 硬件建议
- **学习/开发**: 集成显卡 + GPU加速
- **专业制作**: 独立显卡 + 高质量CPU渲染
- **教学演示**: GPU加速实时预览

## 📈 未来发展

### Manim GPU加速路线图
- ✅ OpenGL渲染器 (已实现)
- 🔄 Vulkan支持 (开发中)
- 🔄 CUDA加速 (计划中)
- 🔄 Metal支持 (macOS, 计划中)

### 新特性预期
- 更好的3D渲染性能
- 实时光线追踪
- 更高效的内存管理
- 跨平台统一API

---

**总结**: GPU加速主要适用于复杂的3D场景和交互式演示。对于HAUV力的视角转换动画，3D演示将从GPU加速中获得显著性能提升！
