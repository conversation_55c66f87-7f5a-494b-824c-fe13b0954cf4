# HAUV力的视角转换动画演示

这是一套用Manim制作的HAUV（混合自主水下航行器）力的视角转换可视化动画系统，用于演示重力和浮力如何通过旋转矩阵从地球坐标系转换到机体坐标系。

## 🎯 核心概念

### 物理背景
- **地球坐标系**: 重力和浮力始终垂直（重力向下，浮力向上）
- **机体坐标系**: HAUV自身的坐标系，随HAUV姿态变化
- **坐标系转换**: 通过旋转矩阵实现力的视角转换
- **恢复力**: 机体坐标系中的力分量产生使HAUV回到水平状态的力矩

### 核心公式
```
g₁ = -R₁ᵀ(fB + fG)
```
其中：
- `g₁`: 机体坐标系中的重力-浮力合力
- `R₁ᵀ`: 旋转矩阵的转置
- `fB`: 浮力向量（地球坐标系）
- `fG`: 重力向量（地球坐标系）

## 📁 文件说明

### 1. `HAUV_Force_Visualization.py` - 基础2D演示
**适合**: 初学者理解基本概念

**内容**:
- 三个经典场景：水平、俯仰、翻滚
- 清晰的数学公式展示
- 力分量的直观表示

**场景演示**:
- 场景一：θ=0°, φ=0° (水平状态)
- 场景二：θ>0° (俯仰抬头)
- 场景三：φ>0° (向右翻滚)

### 2. `HAUV_3D_Force_Visualization.py` - 完整3D演示
**适合**: 深入理解三维空间中的力转换

**特点**:
- 真正的三维空间可视化
- 相机旋转效果
- 组合运动演示（俯仰+翻滚）
- 3D箭头显示力向量

### 3. `HAUV_Interactive_Demo.py` - 交互式演示
**适合**: 详细分析和数值验证

**特点**:
- 7种不同角度组合
- 实时数值计算显示
- 恢复力矩的物理解释
- 参数化演示

**演示角度**:
- 水平状态 (0°, 0°)
- 俯仰30° (30°, 0°)
- 翻滚45° (0°, 45°)
- 组合运动 (20°, 30°)
- 负角度 (-15°, -20°)
- 大角度俯仰 (45°, 0°)
- 大角度翻滚 (0°, 60°)

### 4. `run_hauv_animations.py` - 运行脚本
**功能**: 一键运行所有动画，支持参数配置

## 🚀 使用方法

### 环境要求
```bash
pip install manim
```

### 运行动画

#### 运行所有动画（推荐）
```bash
python GPT_CODE/run_hauv_animations.py
```

#### 运行特定动画
```bash
# 基础2D演示
python GPT_CODE/run_hauv_animations.py --demo 2d

# 3D演示
python GPT_CODE/run_hauv_animations.py --demo 3d

# 交互式演示
python GPT_CODE/run_hauv_animations.py --demo interactive
```

#### 设置渲染质量
```bash
# 低质量（快速预览）
python GPT_CODE/run_hauv_animations.py --quality low

# 中等质量（默认）
python GPT_CODE/run_hauv_animations.py --quality medium

# 高质量（最终版本）
python GPT_CODE/run_hauv_animations.py --quality high
```

#### 指定输出目录
```bash
python GPT_CODE/run_hauv_animations.py --output-dir ./my_videos
```

### 直接运行Manim
```bash
# 运行单个场景
manim -ql GPT_CODE/HAUV_Force_Visualization.py HAUVForceVisualization

# 高质量渲染
manim -qh GPT_CODE/HAUV_3D_Force_Visualization.py HAUV3DForceVisualization
```

## 🔬 物理意义解释

### 场景分析

#### 1. 水平状态 (θ=0°, φ=0°)
- **机体坐标系感受**: 纯垂直力
- **力分量**: g₁ₓ=0, g₁ᵧ=0, g₁ᵤ=(m-kₛρV)g
- **物理意义**: 无恢复力矩，稳定状态

#### 2. 俯仰状态 (θ>0°)
- **机体坐标系感受**: X轴出现分量
- **力分量**: g₁ₓ=-(m-kₛρV)g·sinθ < 0
- **物理意义**: X轴负分量产生俯仰恢复力矩，使HAUV机头下压

#### 3. 翻滚状态 (φ>0°)
- **机体坐标系感受**: Y轴出现分量
- **力分量**: g₁ᵧ=(m-kₛρV)g·sinφ > 0
- **物理意义**: Y轴正分量产生翻滚恢复力矩，使HAUV向左回滚

### 数学推导
旋转矩阵展开：
```
R₁ = [cosθ·cosφ   -sinφ   sinθ·cosφ]
     [cosθ·sinφ    cosφ   sinθ·sinφ]
     [-sinθ         0      cosθ    ]
```

力分量计算：
```
g₁ₓ = -(m-kₛρV)g · sinθ
g₁ᵧ = (m-kₛρV)g · cosθ · sinφ  
g₁ᵤ = (m-kₛρV)g · cosθ · cosφ
```

## 📊 输出文件

动画文件将保存在：
```
media/videos/[场景名称]/[质量]/[场景名称].mp4
```

例如：
- `media/videos/HAUVForceVisualization/480p15/HAUVForceVisualization.mp4`
- `media/videos/HAUV3DForceVisualization/480p15/HAUV3DForceVisualization.mp4`
- `media/videos/HAUVInteractiveDemo/480p15/HAUVInteractiveDemo.mp4`

## 🎓 教学建议

### 使用顺序
1. **先看2D演示** - 理解基本概念
2. **再看3D演示** - 理解三维空间关系
3. **最后看交互式演示** - 深入理解数值关系

### 重点关注
- 地球坐标系中力的恒定性
- 机体坐标系中力分量的变化
- 恢复力矩的产生机制
- 旋转矩阵的作用

## 🔧 故障排除

### 常见问题
1. **ffmpeg警告**: 不影响动画生成，可忽略
2. **渲染缓慢**: 使用`--quality low`进行快速预览
3. **内存不足**: 关闭其他程序，或降低渲染质量

### 技术支持
如遇问题，请检查：
- Manim版本兼容性
- Python环境配置
- 文件路径正确性

---

**作者**: AI Assistant  
**用途**: HAUV力学概念教学演示  
**版本**: 1.0  
**日期**: 2025-08-07
