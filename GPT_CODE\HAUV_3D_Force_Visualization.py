from manim import *
import numpy as np

class HAUV3DForceVisualization(ThreeDScene):
    def construct(self):
        # 设置3D场景
        self.set_camera_orientation(phi=75 * DEGREES, theta=45 * DEGREES)
        
        # 标题
        title = Text("HAUV 3D力的视角转换", font_size=32, color=BLUE)
        title.to_edge(UP)
        self.add_fixed_in_frame_mobjects(title)
        
        # 创建3D坐标系（地球坐标系）
        axes = ThreeDAxes(
            x_range=[-3, 3, 1],
            y_range=[-3, 3, 1],
            z_range=[-3, 3, 1],
            x_length=6,
            y_length=6,
            z_length=6,
            axis_config={"color": GRAY}
        )
        
        # 坐标系标签
        x_label = Text("X", font_size=20, color=RED).next_to(axes.x_axis, RIGHT)
        y_label = Text("Y", font_size=20, color=GREEN).next_to(axes.y_axis, UP)
        z_label = Text("Z", font_size=20, color=BLUE).next_to(axes.z_axis, OUT)
        
        self.play(Create(axes))
        self.add(x_label, y_label, z_label)
        
        # 创建HAUV（3D盒子）
        hauv = Cube(side_length=1, color=YELLOW, fill_opacity=0.7)
        hauv.scale([2, 0.8, 0.5])  # 拉长成潜艇形状
        
        # HAUV机体坐标系
        body_x = Arrow3D(ORIGIN, [1.5, 0, 0], color=RED)
        body_y = Arrow3D(ORIGIN, [0, 1.5, 0], color=GREEN)
        body_z = Arrow3D(ORIGIN, [0, 0, 1.5], color=BLUE)
        
        body_axes = VGroup(body_x, body_y, body_z)
        hauv_group = VGroup(hauv, body_axes)
        
        self.play(Create(hauv), Create(body_axes))
        
        # 重力和浮力（在地球坐标系中始终垂直）
        gravity_vec = Arrow3D(ORIGIN, [0, 0, -2], color=ORANGE, thickness=0.02)
        buoyancy_vec = Arrow3D(ORIGIN, [0, 0, 1.5], color=LIGHT_BLUE, thickness=0.02)
        net_force_vec = Arrow3D(ORIGIN, [0, 0, -0.5], color=PURPLE, thickness=0.03)
        
        # 力的标签（固定在屏幕上）
        gravity_label = Text("重力 fG", font_size=18, color=ORANGE)
        buoyancy_label = Text("浮力 fB", font_size=18, color=LIGHT_BLUE)
        net_force_label = Text("净力", font_size=18, color=PURPLE)
        
        gravity_label.to_edge(LEFT, buff=0.5).shift(UP * 2)
        buoyancy_label.next_to(gravity_label, DOWN)
        net_force_label.next_to(buoyancy_label, DOWN)
        
        self.add_fixed_in_frame_mobjects(gravity_label, buoyancy_label, net_force_label)
        
        self.play(
            Create(gravity_vec), Create(buoyancy_vec), Create(net_force_vec)
        )
        self.wait(2)
        
        # 场景一：水平状态
        scenario1 = Text("场景一：水平状态 (θ=0°, φ=0°)", font_size=20, color=WHITE)
        scenario1.to_edge(UP, buff=1.5)
        self.add_fixed_in_frame_mobjects(scenario1)
        
        # 机体坐标系中的力分量显示
        components_title = Text("机体坐标系力分量:", font_size=16, color=YELLOW)
        components_title.to_edge(RIGHT, buff=0.5).shift(UP * 2)
        
        comp_x = MathTex("g_{1x} = 0", font_size=14, color=RED)
        comp_y = MathTex("g_{1y} = 0", font_size=14, color=GREEN)
        comp_z = MathTex("g_{1z} = (m-k_s\\rho V)g", font_size=14, color=BLUE)
        
        comp_x.next_to(components_title, DOWN, aligned_edge=LEFT)
        comp_y.next_to(comp_x, DOWN, aligned_edge=LEFT)
        comp_z.next_to(comp_y, DOWN, aligned_edge=LEFT)
        
        self.add_fixed_in_frame_mobjects(components_title, comp_x, comp_y, comp_z)
        self.wait(3)
        
        # 场景二：俯仰旋转
        scenario2 = Text("场景二：俯仰旋转 (θ=30°)", font_size=20, color=WHITE)
        self.remove(scenario1)
        self.add_fixed_in_frame_mobjects(scenario2)
        
        # 绕Y轴旋转（俯仰）
        pitch_angle = PI/6
        self.play(
            Rotate(hauv_group, pitch_angle, axis=UP, about_point=ORIGIN),
            run_time=2
        )
        
        # 更新力分量
        new_comp_x = MathTex("g_{1x} = -(m-k_s\\rho V)g\\sin\\theta", font_size=12, color=RED)
        new_comp_y = MathTex("g_{1y} = 0", font_size=14, color=GREEN)
        new_comp_z = MathTex("g_{1z} = (m-k_s\\rho V)g\\cos\\theta", font_size=12, color=BLUE)
        
        new_comp_x.next_to(components_title, DOWN, aligned_edge=LEFT)
        new_comp_y.next_to(new_comp_x, DOWN, aligned_edge=LEFT)
        new_comp_z.next_to(new_comp_y, DOWN, aligned_edge=LEFT)
        
        self.remove(comp_x, comp_y, comp_z)
        self.add_fixed_in_frame_mobjects(new_comp_x, new_comp_y, new_comp_z)
        
        # 显示机体坐标系中的力分量向量
        body_force_x = Arrow3D(ORIGIN, [-0.5, 0, 0], color=RED, thickness=0.02)
        body_force_z = Arrow3D(ORIGIN, [0, 0, -0.8], color=BLUE, thickness=0.02)
        
        # 旋转这些向量以匹配机体坐标系
        body_force_x.rotate(pitch_angle, axis=UP, about_point=ORIGIN)
        body_force_z.rotate(pitch_angle, axis=UP, about_point=ORIGIN)
        
        self.play(Create(body_force_x), Create(body_force_z))
        
        # 恢复力矩说明
        moment_text = Text("X分量产生俯仰恢复力矩", font_size=14, color=RED)
        moment_text.to_edge(RIGHT, buff=0.5).shift(DOWN * 1)
        self.add_fixed_in_frame_mobjects(moment_text)
        
        self.wait(3)
        
        # 场景三：翻滚旋转
        scenario3 = Text("场景三：翻滚旋转 (φ=45°)", font_size=20, color=WHITE)
        self.remove(scenario2)
        self.add_fixed_in_frame_mobjects(scenario3)
        
        # 先回到水平状态
        self.play(
            Rotate(hauv_group, -pitch_angle, axis=UP, about_point=ORIGIN),
            FadeOut(body_force_x), FadeOut(body_force_z)
        )
        self.remove(moment_text)
        
        # 绕X轴旋转（翻滚）
        roll_angle = PI/4
        self.play(
            Rotate(hauv_group, roll_angle, axis=RIGHT, about_point=ORIGIN),
            run_time=2
        )
        
        # 更新力分量
        roll_comp_x = MathTex("g_{1x} = 0", font_size=14, color=RED)
        roll_comp_y = MathTex("g_{1y} = (m-k_s\\rho V)g\\sin\\phi", font_size=12, color=GREEN)
        roll_comp_z = MathTex("g_{1z} = (m-k_s\\rho V)g\\cos\\phi", font_size=12, color=BLUE)
        
        roll_comp_x.next_to(components_title, DOWN, aligned_edge=LEFT)
        roll_comp_y.next_to(roll_comp_x, DOWN, aligned_edge=LEFT)
        roll_comp_z.next_to(roll_comp_y, DOWN, aligned_edge=LEFT)
        
        self.remove(new_comp_x, new_comp_y, new_comp_z)
        self.add_fixed_in_frame_mobjects(roll_comp_x, roll_comp_y, roll_comp_z)
        
        # 显示Y轴力分量
        body_force_y = Arrow3D(ORIGIN, [0, 0.6, 0], color=GREEN, thickness=0.02)
        body_force_z2 = Arrow3D(ORIGIN, [0, 0, -0.7], color=BLUE, thickness=0.02)
        
        body_force_y.rotate(roll_angle, axis=RIGHT, about_point=ORIGIN)
        body_force_z2.rotate(roll_angle, axis=RIGHT, about_point=ORIGIN)
        
        self.play(Create(body_force_y), Create(body_force_z2))
        
        # 翻滚恢复力矩说明
        roll_moment_text = Text("Y分量产生翻滚恢复力矩", font_size=14, color=GREEN)
        roll_moment_text.to_edge(RIGHT, buff=0.5).shift(DOWN * 1)
        self.add_fixed_in_frame_mobjects(roll_moment_text)
        
        self.wait(3)
        
        # 组合运动：俯仰+翻滚
        scenario4 = Text("场景四：组合运动 (θ=20°, φ=30°)", font_size=20, color=WHITE)
        self.remove(scenario3)
        self.add_fixed_in_frame_mobjects(scenario4)
        
        # 先回到水平
        self.play(
            Rotate(hauv_group, -roll_angle, axis=RIGHT, about_point=ORIGIN),
            FadeOut(body_force_y), FadeOut(body_force_z2)
        )
        self.remove(roll_moment_text)
        
        # 组合旋转
        combined_pitch = PI/9  # 20度
        combined_roll = PI/6   # 30度
        
        self.play(
            Rotate(hauv_group, combined_pitch, axis=UP, about_point=ORIGIN),
            run_time=1
        )
        self.play(
            Rotate(hauv_group, combined_roll, axis=RIGHT, about_point=ORIGIN),
            run_time=1
        )
        
        # 显示所有三个分量
        combined_force_x = Arrow3D(ORIGIN, [-0.3, 0, 0], color=RED, thickness=0.02)
        combined_force_y = Arrow3D(ORIGIN, [0, 0.4, 0], color=GREEN, thickness=0.02)
        combined_force_z = Arrow3D(ORIGIN, [0, 0, -0.6], color=BLUE, thickness=0.02)
        
        # 应用组合旋转
        combined_force_x.rotate(combined_pitch, axis=UP, about_point=ORIGIN)
        combined_force_x.rotate(combined_roll, axis=RIGHT, about_point=ORIGIN)
        
        combined_force_y.rotate(combined_pitch, axis=UP, about_point=ORIGIN)
        combined_force_y.rotate(combined_roll, axis=RIGHT, about_point=ORIGIN)
        
        combined_force_z.rotate(combined_pitch, axis=UP, about_point=ORIGIN)
        combined_force_z.rotate(combined_roll, axis=RIGHT, about_point=ORIGIN)
        
        self.play(
            Create(combined_force_x),
            Create(combined_force_y),
            Create(combined_force_z)
        )
        
        # 最终公式
        final_formula = MathTex("g_1 = -R_1^T(f_B + f_G)", font_size=20, color=WHITE)
        final_formula.to_edge(DOWN, buff=1)
        self.add_fixed_in_frame_mobjects(final_formula)
        
        # 旋转相机以更好地观察
        self.begin_ambient_camera_rotation(rate=0.1)
        self.wait(5)
        self.stop_ambient_camera_rotation()
        
        self.wait(2)

if __name__ == "__main__":
    scene = HAUV3DForceVisualization()
    scene.render()
