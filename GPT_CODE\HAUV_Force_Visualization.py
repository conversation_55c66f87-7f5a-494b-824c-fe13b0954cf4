from manim import *
import numpy as np

class HAUVForceVisualization(Scene):
    def construct(self):
        # 设置标题
        title = Text("HAUV力的视角转换", font_size=36, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 创建坐标系
        axes = Axes(
            x_range=[-4, 4, 1],
            y_range=[-4, 4, 1],
            x_length=6,
            y_length=6,
            axis_config={"color": GRAY}
        )
        axes.move_to(ORIGIN)
        
        # 地球坐标系标签
        earth_label = Text("地球坐标系", font_size=24, color=GREEN)
        earth_label.next_to(axes, DOWN, buff=0.5)
        
        self.play(Create(axes), Write(earth_label))
        
        # 创建HAUV（简化为矩形）
        hauv = Rectangle(width=2, height=0.8, color=YELLOW, fill_opacity=0.7)
        hauv.move_to(ORIGIN)
        
        # HAUV机体坐标系轴
        body_x_axis = Arrow(ORIGIN, RIGHT * 1.5, color=RED, buff=0)
        body_y_axis = Arrow(ORIGIN, UP * 1.5, color=BLUE, buff=0)
        body_z_axis = Arrow(ORIGIN, OUT * 1.5, color=GREEN, buff=0)  # 用于3D效果
        
        # 机体坐标系标签
        body_x_label = Text("x", font_size=20, color=RED).next_to(body_x_axis, RIGHT)
        body_y_label = Text("y", font_size=20, color=BLUE).next_to(body_y_axis, UP)
        body_z_label = Text("z", font_size=20, color=GREEN).next_to(body_z_axis, OUT)
        
        body_axes = VGroup(body_x_axis, body_y_axis, body_x_label, body_y_label)
        
        self.play(Create(hauv), Create(body_axes))
        
        # 重力和浮力向量（在地球坐标系中）
        gravity = Arrow(ORIGIN, DOWN * 2, color=ORANGE, buff=0, stroke_width=6)
        buoyancy = Arrow(ORIGIN, UP * 1.5, color=BLUE_C, buff=0, stroke_width=6)
        
        gravity_label = Text("重力 fG", font_size=20, color=ORANGE)
        gravity_label.next_to(gravity, DOWN)
        buoyancy_label = Text("浮力 fB", font_size=20, color=BLUE_C)
        buoyancy_label.next_to(buoyancy, UP)
        
        # 净力向量
        net_force = Arrow(ORIGIN, DOWN * 0.5, color=PURPLE, buff=0, stroke_width=8)
        net_force_label = Text("净力", font_size=20, color=PURPLE)
        net_force_label.next_to(net_force, LEFT)
        
        self.play(
            Create(gravity), Write(gravity_label),
            Create(buoyancy), Write(buoyancy_label),
            Create(net_force), Write(net_force_label)
        )
        self.wait(2)
        
        # 场景一：水平状态
        scenario_text = Text("场景一：HAUV完全水平 (θ=0°, φ=0°)", font_size=24, color=WHITE)
        scenario_text.to_edge(UP, buff=1.5)
        
        self.play(Transform(title, scenario_text))
        
        # 在机体坐标系中显示力分量
        force_components_text = Text("机体坐标系中的力分量:", font_size=20, color=YELLOW)
        force_components_text.to_edge(LEFT, buff=0.5).shift(UP * 2)
        
        fx_text = MathTex("g_{1x} = 0", font_size=18, color=RED)
        fy_text = MathTex("g_{1y} = 0", font_size=18, color=BLUE)
        fz_text = MathTex("g_{1z} = (m-k_s\\rho V)g", font_size=18, color=GREEN)
        
        fx_text.next_to(force_components_text, DOWN, aligned_edge=LEFT)
        fy_text.next_to(fx_text, DOWN, aligned_edge=LEFT)
        fz_text.next_to(fy_text, DOWN, aligned_edge=LEFT)
        
        self.play(
            Write(force_components_text),
            Write(fx_text), Write(fy_text), Write(fz_text)
        )
        self.wait(3)
        
        # 场景二：俯仰（抬头）
        scenario2_text = Text("场景二：HAUV俯仰抬头 (θ>0°)", font_size=24, color=WHITE)
        self.play(Transform(title, scenario2_text))
        
        # 旋转HAUV和机体坐标系
        pitch_angle = PI/6  # 30度
        
        # 创建旋转组
        hauv_group = VGroup(hauv, body_axes)
        
        self.play(
            Rotate(hauv_group, pitch_angle, about_point=ORIGIN),
            run_time=2
        )
        
        # 更新力分量文本
        new_fx_text = MathTex("g_{1x} = -(m-k_s\\rho V)g\\sin\\theta < 0", font_size=16, color=RED)
        new_fy_text = MathTex("g_{1y} = 0", font_size=18, color=BLUE)
        new_fz_text = MathTex("g_{1z} = (m-k_s\\rho V)g\\cos\\theta", font_size=16, color=GREEN)
        
        new_fx_text.next_to(force_components_text, DOWN, aligned_edge=LEFT)
        new_fy_text.next_to(new_fx_text, DOWN, aligned_edge=LEFT)
        new_fz_text.next_to(new_fy_text, DOWN, aligned_edge=LEFT)
        
        self.play(
            Transform(fx_text, new_fx_text),
            Transform(fy_text, new_fy_text),
            Transform(fz_text, new_fz_text)
        )
        
        # 显示机体坐标系中的力分量向量
        body_fx = Arrow(ORIGIN, LEFT * 0.8, color=RED, buff=0, stroke_width=4)
        body_fz = Arrow(ORIGIN, DOWN * 1.2, color=GREEN, buff=0, stroke_width=4)
        
        # 旋转这些向量以匹配机体坐标系
        body_fx.rotate(pitch_angle, about_point=ORIGIN)
        body_fz.rotate(pitch_angle, about_point=ORIGIN)
        
        self.play(Create(body_fx), Create(body_fz))
        
        # 添加说明文字
        explanation = Text("x轴分量产生恢复力矩", font_size=18, color=RED)
        explanation.to_edge(RIGHT, buff=0.5).shift(UP * 1)
        self.play(Write(explanation))
        
        self.wait(3)
        
        # 场景三：翻滚
        scenario3_text = Text("场景三：HAUV向右翻滚 (φ>0°)", font_size=24, color=WHITE)
        self.play(Transform(title, scenario3_text))
        
        # 先回到水平状态，然后翻滚
        self.play(
            Rotate(hauv_group, -pitch_angle, about_point=ORIGIN),
            FadeOut(body_fx), FadeOut(body_fz), FadeOut(explanation)
        )
        
        roll_angle = PI/4  # 45度翻滚
        
        # 模拟翻滚（在2D中用倾斜表示）
        self.play(
            Rotate(hauv_group, roll_angle, about_point=ORIGIN),
            run_time=2
        )
        
        # 更新力分量文本
        roll_fx_text = MathTex("g_{1x} = 0", font_size=18, color=RED)
        roll_fy_text = MathTex("g_{1y} = (m-k_s\\rho V)g\\sin\\phi > 0", font_size=16, color=BLUE)
        roll_fz_text = MathTex("g_{1z} = (m-k_s\\rho V)g\\cos\\phi", font_size=16, color=GREEN)
        
        roll_fx_text.next_to(force_components_text, DOWN, aligned_edge=LEFT)
        roll_fy_text.next_to(roll_fx_text, DOWN, aligned_edge=LEFT)
        roll_fz_text.next_to(roll_fy_text, DOWN, aligned_edge=LEFT)
        
        self.play(
            Transform(fx_text, roll_fx_text),
            Transform(fy_text, roll_fy_text),
            Transform(fz_text, roll_fz_text)
        )
        
        # 显示y轴分量
        body_fy = Arrow(ORIGIN, UP * 0.8, color=BLUE, buff=0, stroke_width=4)
        body_fy.rotate(roll_angle, about_point=ORIGIN)
        
        self.play(Create(body_fy))
        
        # 添加说明文字
        roll_explanation = Text("y轴分量产生滚转恢复力矩", font_size=18, color=BLUE)
        roll_explanation.to_edge(RIGHT, buff=0.5).shift(UP * 1)
        self.play(Write(roll_explanation))
        
        self.wait(3)
        
        # 总结
        summary_text = Text("核心思想：旋转矩阵将地球坐标系的力转换为机体坐标系", 
                          font_size=20, color=YELLOW)
        summary_text.to_edge(DOWN, buff=0.5)
        
        formula = MathTex("g_1 = -R_1^T(f_B + f_G)", font_size=24, color=WHITE)
        formula.next_to(summary_text, UP)
        
        self.play(Write(summary_text), Write(formula))
        self.wait(4)

if __name__ == "__main__":
    # 渲染场景
    scene = HAUVForceVisualization()
    scene.render()
